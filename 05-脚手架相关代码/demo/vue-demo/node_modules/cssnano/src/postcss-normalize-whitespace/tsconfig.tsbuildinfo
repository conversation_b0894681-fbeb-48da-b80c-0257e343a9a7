{"program": {"fileNames": ["../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/.pnpm/typescript@4.6.4/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/index.d.ts", "../../node_modules/.pnpm/source-map-js@1.0.2/node_modules/source-map-js/source-map.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/comment.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/rule.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/container.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/declaration.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/input.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/warning.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/document.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/root.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/processor.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/result.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/node.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/list.d.ts", "../../node_modules/.pnpm/postcss@8.4.13/node_modules/postcss/lib/postcss.d.ts", "./src/index.js", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/assert.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/assert/strict.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/globals.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/async_hooks.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/buffer.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/child_process.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/cluster.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/console.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/constants.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/crypto.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/dgram.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/dns.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/dns/promises.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/domain.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/events.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/fs.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/fs/promises.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/http.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/http2.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/https.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/inspector.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/module.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/net.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/os.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/path.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/process.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/punycode.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/querystring.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/readline.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/repl.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/stream.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/stream/promises.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/stream/web.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/string_decoder.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/timers.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/timers/promises.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/tls.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/trace_events.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/tty.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/url.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/util.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/v8.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/vm.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/wasi.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/worker_threads.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/zlib.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/globals.global.d.ts", "../../node_modules/.pnpm/@types+node@17.0.31/node_modules/@types/node/index.d.ts"], "fileInfos": [{"version": "3ac1b83264055b28c0165688fda6dfcc39001e9e7828f649299101c23ad0a0c3", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "e6b724280c694a9f588847f754198fb96c43d805f065c3a5b28bbc9594541c84", "e21c071ca3e1b4a815d5f04a7475adcaeea5d64367e840dd0154096d705c3940", {"version": "d8996609230d17e90484a2dd58f22668f9a05a3bfe00bfb1d6271171e54a31fb", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "0d5f52b3174bee6edb81260ebcd792692c32c81fd55499d69531496f3f2b25e7", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "62d80405c46c3f4c527ee657ae9d43fda65a0bf582292429aea1e69144a522a6", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "6e7997ef61de3132e4d4b2250e75343f487903ddf5370e7ce33cf1b9db9a63ed", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "1b3fe904465430e030c93239a348f05e1be80640d91f2f004c3512c2c2c89f34", "affectsGlobalScope": true}, {"version": "3787b83e297de7c315d55d4a7c546ae28e5f6c0a361b7a1dcec1f1f50a54ef11", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "5075b36ab861c8c0c45377cb8c96270d7c65f0eeaf105d53fac6850da61f1027", "affectsGlobalScope": true}, {"version": "10bbdc1981b8d9310ee75bfac28ee0477bb2353e8529da8cff7cb26c409cb5e8", "affectsGlobalScope": true}, "4533b61de4b1594a736704184d03d4d8d5f3b072a6acfcfd41d76da975ac04fd", "858d0d831826c6eb563df02f7db71c90e26deadd0938652096bea3cc14899700", "d1c89db652113258e4ba4bbdf5cc7a2a3a600403d4d864a2087b95186253cd5b", "11a90d2cb2eaf7fdf931a63b58279e8161f1477a1bd1e914ae026c1bbf9afed3", "af18e30f3ba06e9870b61dfa4a109215caabdaa337590c51b4a044a9f338ce96", "ace603f7b60599f2dcdbd71c07137b60a747dd33be540f4a294b890f9e0b89dc", "7658fbdd425c656fb1849b44932ae7431e8c3198d22c65ce1490deb582743b52", "7786c75c1b46e93b33c63dccf689143a5f47ff451a6b3bd9b10e4801cdeadcc2", "615b7264db151461b896cd79719414d63f7b2b2100b275828e53cab95a356e2f", "31491a01ed7466e0b3b0ef8407f2524683055eceb955b1d5ccf7096129468b39", "f4b12f7dde4fc0e386648318481bdcfe861b566be246bebf0e8a11ebd909adf9", "e8966f7c424780bb0b9d411ebe13eda8555ca15aa675603316c2952bc027b0e3", "df0e5f3c4a518111d160cf3bebc9a3ac7d39c6e3bfb7a21d43c304896c3015e2", "df4e2f161f74870708c2cc5e1036a6405b878496408fda1ee50d5b10e50d6601", "bf791da347fb1c0ffc1e2fcd35867e64bb8355270ae26278198c521bdcf94569", "e0e0e3c068e145fbb322120979299ff130ffdd39f0dcd0d5aeaa9f3f8a0d01d9", "fde91356172e35b9ea68bbdf33721f7c80307a4ce65b82105eac800e9e744995", "9bd5e5a4a1e66b35efe3c48ddac1116537ef86e041717f3a9b9f1e060c74efa6", "d7e4a5f4ccfb749c3033fafc233073b4d1dcca0249785186c589602a81f9d86f", "68161b6f3004fc10f8bb47a4986cef13c3b0728fb1ca3e1dc7316227d09b2c8d", "a9d7915871d71c91cdc82f6ec46257463ff9c0c97ef690a5764015353cc3ce41", "0cba3a5d7b81356222594442753cf90dd2892e5ccfe1d262aaca6896ba6c1380", "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", {"version": "77f0b5c6a193a699c9f7d7fb0578e64e562d271afa740783665d2a827104a873", "affectsGlobalScope": true}, "e5979905796fe2740d85fbaf4f11f42b7ee1851421afe750823220813421b1af", {"version": "fcdcb42da18dd98dc286b1876dd425791772036012ae61263c011a76b13a190f", "affectsGlobalScope": true}, "1dab5ab6bcf11de47ab9db295df8c4f1d92ffa750e8f095e88c71ce4c3299628", "f71f46ccd5a90566f0a37b25b23bc4684381ab2180bdf6733f4e6624474e1894", {"version": "54e65985a3ee3cec182e6a555e20974ea936fc8b8d1738c14e8ed8a42bd921d4", "affectsGlobalScope": true}, "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "5b30f550565fd0a7524282c81c27fe8534099e2cd26170ca80852308f07ae68d", "34e5de87d983bc6aefef8b17658556e3157003e8d9555d3cb098c6bef0b5fbc8", "d97cd8a4a42f557fc62271369ed0461c8e50d47b7f9c8ad0b5462f53306f6060", "f27371653aded82b2b160f7a7033fb4a5b1534b6f6081ef7be1468f0f15327d3", "c762cd6754b13a461c54b59d0ae0ab7aeef3c292c6cf889873f786ee4d8e75c9", "f4ea7d5df644785bd9fbf419930cbaec118f0d8b4160037d2339b8e23c059e79", {"version": "bfea28e6162ed21a0aeed181b623dcf250aa79abf49e24a6b7e012655af36d81", "affectsGlobalScope": true}, "b8aca9d0c81abb02bec9b7621983ae65bde71da6727580070602bd2500a9ce2a", "ae97e20f2e10dbeec193d6a2f9cd9a367a1e293e7d6b33b68bacea166afd7792", "10d4796a130577d57003a77b95d8723530bbec84718e364aa2129fa8ffba0378", "063f53ff674228c190efa19dd9448bcbd540acdbb48a928f4cf3a1b9f9478e43", "bf73c576885408d4a176f44a9035d798827cc5020d58284cb18d7573430d9022", "7ae078ca42a670445ae0c6a97c029cb83d143d62abd1730efb33f68f0b2c0e82", {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true}, "287b21dc1d1b9701c92e15e7dd673dfe6044b15812956377adffb6f08825b1bc", "12eea70b5e11e924bb0543aea5eadc16ced318aa26001b453b0d561c2fd0bd1e", "08777cd9318d294646b121838574e1dd7acbb22c21a03df84e1f2c87b1ad47f2", "08a90bcdc717df3d50a2ce178d966a8c353fd23e5c392fd3594a6e39d9bb6304", {"version": "4cd4cff679c9b3d9239fd7bf70293ca4594583767526916af8e5d5a47d0219c7", "affectsGlobalScope": true}, "2a12d2da5ac4c4979401a3f6eaafa874747a37c365e4bc18aa2b171ae134d21b", "002b837927b53f3714308ecd96f72ee8a053b8aeb28213d8ec6de23ed1608b66", "1dc9c847473bb47279e398b22c740c83ea37a5c88bf66629666e3cf4c5b9f99c", "a9e4a5a24bf2c44de4c98274975a1a705a0abbaad04df3557c2d3cd8b1727949", "00fa7ce8bc8acc560dc341bbfdf37840a8c59e6a67c9bfa3fa5f36254df35db2", "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", {"version": "806ef4cac3b3d9fa4a48d849c8e084d7c72fcd7b16d76e06049a9ed742ff79c0", "affectsGlobalScope": true}, "44b8b584a338b190a59f4f6929d072431950c7bd92ec2694821c11bce180c8a5", "5f0ed51db151c2cdc4fa3bb0f44ce6066912ad001b607a34e65a96c52eb76248", {"version": "3345c276cab0e76dda86c0fb79104ff915a4580ba0f3e440870e183b1baec476", "affectsGlobalScope": true}, "664d8f2d59164f2e08c543981453893bc7e003e4dfd29651ce09db13e9457980", "103d70bfbeb3cd3a3f26d1705bf986322d8738c2c143f38ebb743b1e228d7444", "f52fbf64c7e480271a9096763c4882d356b05cab05bf56a64e68a95313cd2ce2", "59bdb65f28d7ce52ccfc906e9aaf422f8b8534b2d21c32a27d7819be5ad81df7", {"version": "3a2da34079a2567161c1359316a32e712404b56566c45332ac9dcee015ecce9f", "affectsGlobalScope": true}, "28a2e7383fd898c386ffdcacedf0ec0845e5d1a86b5a43f25b86bc315f556b79", "3aff9c8c36192e46a84afe7b926136d520487155154ab9ba982a8b544ea8fc95", "a880cf8d85af2e4189c709b0fea613741649c0e40fffb4360ec70762563d5de0", "85bbf436a15bbeda4db888be3062d47f99c66fd05d7c50f0f6473a9151b6a070", "9f9c49c95ecd25e0cb2587751925976cf64fd184714cb11e213749c80cf0f927", "f0c75c08a71f9212c93a719a25fb0320d53f2e50ca89a812640e08f8ad8c408c", {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true}, "9cafe917bf667f1027b2bb62e2de454ecd2119c80873ad76fc41d941089753b8"], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationDir": "./types", "emitDeclarationOnly": true, "module": 1, "newLine": 1, "noEmitOnError": true, "rootDir": "./src", "strict": true, "target": 5}, "fileIdsList": [[60, 103], [63, 103], [64, 69, 103], [65, 75, 76, 83, 92, 102, 103], [65, 66, 75, 83, 103], [67, 103], [68, 69, 76, 84, 103], [69, 92, 99, 103], [70, 72, 75, 83, 103], [71, 103], [72, 73, 103], [74, 75, 103], [75, 103], [75, 76, 77, 92, 102, 103], [75, 76, 77, 92, 103], [103, 107], [103], [78, 83, 92, 102, 103], [75, 76, 78, 79, 83, 92, 99, 102, 103], [78, 80, 92, 99, 102, 103], [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109], [75, 81, 103], [82, 102, 103], [72, 75, 83, 92, 103], [84, 103], [85, 103], [63, 86, 103], [87, 101, 103, 107], [88, 103], [89, 103], [75, 90, 103], [90, 91, 103, 105], [75, 92, 93, 94, 103], [92, 94, 103], [92, 93, 103], [95, 103], [96, 103], [75, 97, 98, 103], [97, 98, 103], [69, 83, 92, 99, 103], [100, 103], [83, 101, 103], [64, 78, 89, 102, 103], [69, 103], [92, 103, 104], [103, 105], [103, 106], [64, 69, 75, 77, 86, 92, 102, 103, 105, 107], [92, 103, 108], [44, 103], [44, 56, 103], [41, 42, 43, 45, 56, 103], [47, 103], [44, 51, 55, 58, 103], [46, 58, 103], [49, 51, 54, 55, 58, 103], [49, 51, 52, 54, 55, 58, 103], [41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 55, 58, 103], [40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 54, 55, 56, 57, 103], [40, 58, 103], [51, 52, 53, 55, 58, 103], [54, 58, 103], [44, 50, 55, 58, 103], [48, 56, 103], [40, 103], [39, 58, 103]], "referencedMap": [[60, 1], [61, 1], [63, 2], [64, 3], [65, 4], [66, 5], [67, 6], [68, 7], [69, 8], [70, 9], [71, 10], [72, 11], [73, 11], [74, 12], [75, 13], [76, 14], [77, 15], [62, 16], [109, 17], [78, 18], [79, 19], [80, 20], [110, 21], [81, 22], [82, 23], [83, 24], [84, 25], [85, 26], [86, 27], [87, 28], [88, 29], [89, 30], [90, 31], [91, 32], [92, 33], [94, 34], [93, 35], [95, 36], [96, 37], [97, 38], [98, 39], [99, 40], [100, 41], [101, 42], [102, 43], [103, 44], [104, 45], [105, 46], [106, 47], [107, 48], [108, 49], [39, 17], [42, 50], [41, 51], [44, 52], [48, 53], [45, 51], [50, 54], [47, 55], [52, 56], [57, 17], [53, 57], [56, 58], [58, 59], [46, 60], [54, 61], [55, 62], [51, 63], [43, 50], [49, 64], [40, 65], [9, 17], [8, 17], [2, 17], [10, 17], [11, 17], [12, 17], [13, 17], [14, 17], [15, 17], [16, 17], [17, 17], [3, 17], [4, 17], [21, 17], [18, 17], [19, 17], [20, 17], [22, 17], [23, 17], [24, 17], [5, 17], [25, 17], [26, 17], [27, 17], [28, 17], [6, 17], [29, 17], [30, 17], [31, 17], [32, 17], [7, 17], [37, 17], [33, 17], [34, 17], [35, 17], [36, 17], [1, 17], [38, 17], [59, 66]], "exportedModulesMap": [[60, 1], [61, 1], [63, 2], [64, 3], [65, 4], [66, 5], [67, 6], [68, 7], [69, 8], [70, 9], [71, 10], [72, 11], [73, 11], [74, 12], [75, 13], [76, 14], [77, 15], [62, 16], [109, 17], [78, 18], [79, 19], [80, 20], [110, 21], [81, 22], [82, 23], [83, 24], [84, 25], [85, 26], [86, 27], [87, 28], [88, 29], [89, 30], [90, 31], [91, 32], [92, 33], [94, 34], [93, 35], [95, 36], [96, 37], [97, 38], [98, 39], [99, 40], [100, 41], [101, 42], [102, 43], [103, 44], [104, 45], [105, 46], [106, 47], [107, 48], [108, 49], [39, 17], [42, 50], [41, 51], [44, 52], [48, 53], [45, 51], [50, 54], [47, 55], [52, 56], [57, 17], [53, 57], [56, 58], [58, 59], [46, 60], [54, 61], [55, 62], [51, 63], [43, 50], [49, 64], [40, 65], [9, 17], [8, 17], [2, 17], [10, 17], [11, 17], [12, 17], [13, 17], [14, 17], [15, 17], [16, 17], [17, 17], [3, 17], [4, 17], [21, 17], [18, 17], [19, 17], [20, 17], [22, 17], [23, 17], [24, 17], [5, 17], [25, 17], [26, 17], [27, 17], [28, 17], [6, 17], [29, 17], [30, 17], [31, 17], [32, 17], [7, 17], [37, 17], [33, 17], [34, 17], [35, 17], [36, 17], [1, 17], [38, 17], [59, 66]], "semanticDiagnosticsPerFile": [60, 61, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 62, 109, 78, 79, 80, 110, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 94, 93, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 39, 42, 41, 44, 48, 45, 50, 47, 52, 57, 53, 56, 58, 46, 54, 55, 51, 43, 49, 40, 9, 8, 2, 10, 11, 12, 13, 14, 15, 16, 17, 3, 4, 21, 18, 19, 20, 22, 23, 24, 5, 25, 26, 27, 28, 6, 29, 30, 31, 32, 7, 37, 33, 34, 35, 36, 1, 38, 59]}, "version": "4.6.4"}