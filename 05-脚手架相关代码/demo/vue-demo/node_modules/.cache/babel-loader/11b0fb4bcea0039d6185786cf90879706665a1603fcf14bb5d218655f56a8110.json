{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxHeaderNav.vue"], "sourcesContent": ["<template>\n    <!-- 头部导航  -->\n    <div class=\"header wrapper\">\n      <!-- logo -->\n      <div class=\"logo\">\n        <h1>\n          <a href=\"#\">小兔鲜儿</a>\n        </h1>\n      </div>\n      <!-- 导航 -->\n      <div class=\"nav\">\n        <ul>\n          <li><a href=\"#\">首页</a></li>\n          <li><a href=\"#\">生鲜</a></li>\n          <li><a href=\"#\">美食</a></li>\n          <li><a href=\"#\">餐厨</a></li>\n          <li><a href=\"#\">电器</a></li>\n          <li><a href=\"#\">居家</a></li>\n          <li><a href=\"#\">洗护</a></li>\n          <li><a href=\"#\">孕婴</a></li>\n          <li><a href=\"#\">服装</a></li>\n        </ul>\n      </div>\n      <!-- 搜索 -->\n      <div class=\"search\">\n        <span class=\"iconfont icon-search\"></span>\n        <input type=\"text\" placeholder=\"搜一搜\" />\n      </div>\n      <!-- 购物车 -->\n      <div class=\"cart\">\n        <span class=\"iconfont icon-cart-full\"></span>\n        <i>2</i>\n      </div>\n    </div>\n\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n</style>"], "mappings": "AAsCA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}