{"ast": null, "code": "import XtxShortCut from './components/XtxShortCut.vue';\nimport XtxHeaderNav from './components/XtxHeaderNav.vue';\nimport XtxBanner from './components/XtxBanner.vue';\nimport XtxNewGoods from './components/XtxNewGoods.vue';\nimport XtxHotBrand from './components/XtxHotBrand.vue';\nimport XtxTopic from './components/XtxTopic.vue';\nimport XtxFooter from './components/XtxFooter.vue';\nexport default {\n  components: {\n    XtxShortCut,\n    XtxHeaderNav,\n    XtxBanner,\n    XtxNewGoods,\n    XtxHotBrand,\n    XtxTopic,\n    XtxFooter\n  }\n};", "map": {"version": 3, "names": ["XtxShortCut", "XtxHeaderNav", "XtxBanner", "XtxNewGoods", "XtxHotBrand", "XtxTopic", "XtxFooter", "components"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <dev class=\"App\">\r\n    我是首页\r\n    <!-- XtxShortCut  => 快捷链接\r\n\r\n        XtxHeaderNav => 顶部导航\r\n        XtxBanner    => 轮播区域\r\n        XtxNewGoods  => 新鲜好物\r\n        XtxHotBrand  => 热门品牌\r\n        XtxTopic     => 最新专题\r\n        XtxFooter    => 版权底部 \r\n        \r\n        -->\r\n        <XtxShortCut></XtxShortCut>\r\n        <XtxHeaderNav></XtxHeaderNav>\r\n        <XtxBanner></XtxBanner>\r\n        <XtxNewGoods></XtxNewGoods>\r\n        <XtxHotBrand></XtxHotBrand>\r\n        <XtxTopic></XtxTopic>\r\n        <XtxFooter></XtxFooter>\r\n\r\n\r\n      \r\n\r\n  </dev>\r\n</template>\r\n\r\n<script>\r\nimport XtxShortCut from './components/XtxShortCut.vue'\r\nimport XtxHeaderNav from './components/XtxHeaderNav.vue'\r\nimport XtxBanner from './components/XtxBanner.vue'\r\nimport XtxNewGoods from './components/XtxNewGoods.vue'\r\nimport XtxHotBrand from './components/XtxHotBrand.vue'\r\nimport XtxTopic from './components/XtxTopic.vue'\r\nimport XtxFooter from './components/XtxFooter.vue'\r\n\r\nexport default {\r\n\r\n  components: {\r\n    XtxShortCut\r\n    ,XtxHeaderNav\r\n    ,XtxBanner\r\n    ,XtxNewGoods\r\n    ,XtxHotBrand\r\n    ,XtxTopic\r\n    ,XtxFooter\r\n\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style>\r\n\r\n</style>"], "mappings": "AA4BA,OAAAA,WAAA;AACA,OAAAC,YAAA;AACA,OAAAC,SAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,SAAA;AAEA;EAEAC,UAAA;IACAP,WAAA;IACAC,YAAA;IACAC,SAAA;IACAC,WAAA;IACAC,WAAA;IACAC,QAAA;IACAC;EAEA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}