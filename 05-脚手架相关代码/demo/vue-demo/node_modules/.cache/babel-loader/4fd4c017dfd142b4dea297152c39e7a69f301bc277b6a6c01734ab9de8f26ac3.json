{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxHotBrand.vue"], "sourcesContent": ["<template>\n      <!-- 热门品牌 -->\n      <div class=\"hot\">\n      <div class=\"wrapper\">\n        <div class=\"title\">\n          <div class=\"left\">\n            <h3>热门品牌</h3>\n            <p>国际经典 品质认证</p>\n          </div>\n          <div class=\"button\">\n            <a href=\"#\"><i class=\"iconfont icon-arrow-left-bold\"></i></a>\n            <a href=\"#\"><i class=\"iconfont icon-arrow-right-bold\"></i></a>\n          </div>\n        </div>\n        <div class=\"bd\">\n          <ul>\n       \n          </ul>\n        </div>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n/* 热门品牌 */\n.hot {\n  margin-top: 60px;\n  padding-bottom: 40px;\n  overflow: hidden;\n  background-color: #F5F5F5;\n}\n.hot .title {\n  position: relative;\n  margin-bottom: 40px;\n}\n.hot .button {\n  display: flex;\n  position: absolute;\n  right: 0;\n  top: 47px;\n}\n.hot .button a {\n  display: block;\n  width: 20px;\n  height: 20px;\n  background-color: #ddd;\n  text-align: center;\n  line-height: 20px;\n  color: #fff;\n}\n.hot .button a:nth-child(2) {\n  margin-left: 12px;\n  background-color: #00BE9A;\n}\n.hot .bd ul {\n  display: flex;\n  justify-content: space-between;\n}\n.hot .bd li {\n  width: 244px;\n  height: 306px;\n}\n\n</style>"], "mappings": "AAwBA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}