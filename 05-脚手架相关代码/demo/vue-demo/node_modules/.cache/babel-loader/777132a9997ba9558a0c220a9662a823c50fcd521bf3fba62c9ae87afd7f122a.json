{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxHotBrand.vue"], "sourcesContent": ["<template>\n     <!-- 轮播区域 -->\n     <div class=\"banner\">\n      <div class=\"wrapper\">\n        <!-- 图 -->\n        <ul class=\"pic\">\n          <li>\n            <a href=\"#\"><img src=\"@/assets/images/banner1.png\" alt=\"\" /></a>\n          </li>\n          <li>\n            <a href=\"#\"><img src=\"@/assets/images/banner1.png\" alt=\"\" /></a>\n          </li>\n        </ul>\n        <!-- 侧导航 -->\n        <div class=\"subnav\">\n          <ul>\n            <li>\n              <div>\n                <span><a href=\"#\">生鲜</a></span>\n                <span><a href=\"#\">水果</a><a href=\"#\">蔬菜</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">美食</a></span>\n                <span><a href=\"#\">面点</a><a href=\"#\">干果</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">餐厨</a></span>\n                <span><a href=\"#\">数码产品</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">电器</a></span>\n                <span\n                  ><a href=\"#\">床品</a><a href=\"#\">四件套</a\n                  ><a href=\"#\">被枕</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">居家</a></span>\n                <span\n                  ><a href=\"#\">奶粉</a><a href=\"#\">玩具</a\n                  ><a href=\"#\">辅食</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">洗护</a></span>\n                <span\n                  ><a href=\"#\">洗发</a><a href=\"#\">洗护</a\n                  ><a href=\"#\">美妆</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">孕婴</a></span>\n                <span><a href=\"#\">奶粉</a><a href=\"#\">玩具</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">服饰</a></span>\n                <span><a href=\"#\">女装</a><a href=\"#\">男装</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">杂货</a></span>\n                <span><a href=\"#\">户外</a><a href=\"#\">图书</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">品牌</a></span>\n                <span><a href=\"#\">品牌制造</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n          </ul>\n        </div>\n        <!-- 指示器 -->\n        <ol>\n          <li class=\"current\"><i></i></li>\n          <li><i></i></li>\n          <li><i></i></li>\n        </ol>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n</style>"], "mappings": "AA4GA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}