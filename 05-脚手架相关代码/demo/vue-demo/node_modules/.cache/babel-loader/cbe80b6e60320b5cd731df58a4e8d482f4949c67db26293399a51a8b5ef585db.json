{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"li\", {\n    staticClass: \"base-goods-item\"\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/goods1.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]), _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"79\")])])])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "attrs", "href", "src", "require", "alt", "_v", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/BaseGoodItem.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"li\", { staticClass: \"base-goods-item\" }, [\n      _c(\"a\", { attrs: { href: \"#\" } }, [\n        _c(\"div\", { staticClass: \"pic\" }, [\n          _c(\"img\", {\n            attrs: { src: require(\"@/assets/images/goods1.png\"), alt: \"\" },\n          }),\n        ]),\n        _c(\"div\", { staticClass: \"txt\" }, [\n          _c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]),\n          _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"79\")])]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAkB,CAAC,EAAE,CAClDJ,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC/D,CAAC,CAAC,CACH,CAAC,EACFT,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,EACnCV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,EAAEV,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}