{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport './styles/base.css'; // css 样式重置\nimport './styles/common.css'; // 公共全局样式\nimport './assets/iconfont/iconfont.css'; // 字体图标的样式\n\nimport BaseGoodItem from './components/BaseGoodItem.vue';\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "BaseGoodItem", "config", "productionTip", "render", "h", "$mount"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport './styles/base.css' // css 样式重置\r\nimport './styles/common.css' // 公共全局样式\r\nimport './assets/iconfont/iconfont.css' // 字体图标的样式\r\n\r\nimport BaseGoodItem from './components/BaseGoodItem.vue'\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  render: h => h(App),\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,mBAAmB,EAAC;AAC3B,OAAO,qBAAqB,EAAC;AAC7B,OAAO,gCAAgC,EAAC;;AAExC,OAAOC,YAAY,MAAM,+BAA+B;AAExDF,GAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIJ,GAAG,CAAC;EACNK,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACL,GAAG;AACpB,CAAC,CAAC,CAACM,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}