{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxShortCut.vue"], "sourcesContent": ["<template>\n      <!-- 快捷链接  -->\n      <div class=\"shortcut\">\n      <div class=\"wrapper\">\n        <ul>\n          <li><a href=\"#\" class=\"login\">请先登录</a></li>\n          <li><a href=\"#\">免费注册</a></li>\n          <li><a href=\"#\">我的订单</a></li>\n          <li><a href=\"#\">会员中心</a></li>\n          <li><a href=\"#\">帮助中心</a></li>\n          <li><a href=\"#\">在线客服</a></li>\n          <li>\n            <a href=\"#\"\n              ><span class=\"iconfont icon-mobile-phone\"></span>手机版</a\n            >\n          </li>\n        </ul>\n      </div>\n    </div>\n\n\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n\n\n<style>\n\n/* 快捷导航 */\n.shortcut {\n  height: 52px;\n  line-height: 52px;\n  background-color: #333;\n}\n.shortcut .wrapper {\n  display: flex;\n  justify-content: flex-end;\n}\n.shortcut ul {\n  display: flex;\n}\n.shortcut a {\n  padding: 0 15px;\n  border-right: 1px solid #999;\n  color: #fff;\n  font-size: 14px;\n  line-height: 14px;\n}\n.shortcut .login {\n  color: #5EB69C;\n}\n.shortcut .icon-mobile-phone {\n  margin-right: 5px;\n}\n\n</style> "], "mappings": "AAwBA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}