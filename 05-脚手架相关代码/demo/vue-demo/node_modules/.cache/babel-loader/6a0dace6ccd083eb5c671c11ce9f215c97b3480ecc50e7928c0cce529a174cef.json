{"ast": null, "code": "// 作用 导入App.vue 基于App.vue 创建结构渲染index.html文件\n// 导入Vue核心包\n// 导入App.vue 根组件\n\nimport Vue from 'vue';\nimport App from './App.vue';\n\n// 提示 当前处于什么环境 生产环境 开发环境\nVue.config.productionTip = false;\n\n// vue实例化 提供render 方法 基于App.vue 创建结构渲染index.html\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "config", "productionTip", "render", "h", "$mount"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/main.js"], "sourcesContent": ["// 作用 导入App.vue 基于App.vue 创建结构渲染index.html文件\n// 导入Vue核心包\n// 导入App.vue 根组件\n\n\nimport Vue from 'vue'\nimport App from './App.vue'\n\n// 提示 当前处于什么环境 生产环境 开发环境\nVue.config.productionTip = false\n\n// vue实例化 提供render 方法 基于App.vue 创建结构渲染index.html\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n"], "mappings": "AAAA;AACA;AACA;;AAGA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;;AAE3B;AACAD,GAAG,CAACE,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACA,IAAIH,GAAG,CAAC;EACNI,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACJ,GAAG;AACpB,CAAC,CAAC,CAACK,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}