{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxNewGoods.vue"], "sourcesContent": ["<template>\n     <!-- 新鲜好物 -->\n     <div class=\"goods wrapper\">\n      <div class=\"title\">\n        <div class=\"left\">\n          <h3>新鲜好物</h3>\n          <p>新鲜出炉 品质靠谱</p>\n        </div>\n        <div class=\"right\">\n          <a href=\"#\" class=\"more\"\n            >查看全部<span class=\"iconfont icon-arrow-right-bold\"></span\n          ></a>\n        </div>\n      </div>\n      <div class=\"bd\">\n        <ul>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods1.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>KN95级莫兰迪色防护口罩</h4>\n                <p>¥ <span>79</span></p>\n              </div>\n            </a>\n          </li>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods2.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>KN95级莫兰迪色防护口罩</h4>\n                <p>¥ <span>566</span></p>\n              </div>\n            </a>\n          </li>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods3.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>法拉蒙高颜值记事本可定制</h4>\n                <p>¥ <span>58</span></p>\n              </div>\n            </a>\n          </li>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods4.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>科技布布艺沙发</h4>\n                <p>¥ <span>3759</span></p>\n              </div>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n</style>"], "mappings": "AA0DA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}