{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"service\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"价格亲民\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"物流快捷\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"品质新鲜\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"售后无忧\")])])])]), _c(\"div\", {\n    staticClass: \"help\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"dl\", [_c(\"dt\", [_vm._v(\"购物指南\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"购物流程\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"支付方式\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后规则\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"配送方式\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送运费\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送范围\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送时间\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"关于我们\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"平台规则\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"联系我们\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"问题反馈\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"售后服务\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后政策\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"退款说明\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"取消订单\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"服务热线\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"在线客服\"), _c(\"span\", {\n    staticClass: \"iconfont icon-customer-service\"\n  })])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"客服电话 400-0000-000\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"工作时间 周一至周日 8:00-18:00\")])])])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"div\", [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/wechat.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"p\", [_vm._v(\"微信公众号\")])]), _c(\"li\", [_c(\"div\", [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/app.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"p\", [_vm._v(\"APP下载二维码\")])])])])]), _c(\"div\", {\n    staticClass: \"copyright\"\n  }, [_c(\"p\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"关于我们\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"帮助中心\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后服务\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送与验收\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"商务合作\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"搜索推荐\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"友情链接\")])]), _c(\"p\", [_vm._v(\"CopyRight © 小兔鲜\")])])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "_v", "attrs", "href", "src", "require", "alt", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/XtxFooter.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer\" }, [\n      _c(\"div\", { staticClass: \"wrapper\" }, [\n        _c(\"div\", { staticClass: \"service\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"价格亲民\")])]),\n            _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"物流快捷\")])]),\n            _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"品质新鲜\")])]),\n            _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"售后无忧\")])]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"help\" }, [\n          _c(\"div\", { staticClass: \"left\" }, [\n            _c(\"dl\", [\n              _c(\"dt\", [_vm._v(\"购物指南\")]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"购物流程\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"支付方式\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后规则\")]),\n              ]),\n            ]),\n            _c(\"dl\", [\n              _c(\"dt\", [_vm._v(\"配送方式\")]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送运费\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送范围\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送时间\")]),\n              ]),\n            ]),\n            _c(\"dl\", [\n              _c(\"dt\", [_vm._v(\"关于我们\")]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"平台规则\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"联系我们\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"问题反馈\")]),\n              ]),\n            ]),\n            _c(\"dl\", [\n              _c(\"dt\", [_vm._v(\"售后服务\")]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后政策\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"退款说明\")]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"取消订单\")]),\n              ]),\n            ]),\n            _c(\"dl\", [\n              _c(\"dt\", [_vm._v(\"服务热线\")]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _vm._v(\"在线客服\"),\n                  _c(\"span\", { staticClass: \"iconfont icon-customer-service\" }),\n                ]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _vm._v(\"客服电话 400-0000-000\"),\n                ]),\n              ]),\n              _c(\"dd\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _vm._v(\"工作时间 周一至周日 8:00-18:00\"),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"right\" }, [\n            _c(\"ul\", [\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/wechat.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n                _c(\"p\", [_vm._v(\"微信公众号\")]),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"img\", {\n                    attrs: { src: require(\"@/assets/images/app.png\"), alt: \"\" },\n                  }),\n                ]),\n                _c(\"p\", [_vm._v(\"APP下载二维码\")]),\n              ]),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"copyright\" }, [\n          _c(\"p\", [\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"关于我们\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"帮助中心\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后服务\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送与验收\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"商务合作\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"搜索推荐\")]),\n            _vm._v(\"|\"),\n            _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"友情链接\")]),\n          ]),\n          _c(\"p\", [_vm._v(\"CopyRight © 小兔鲜\")]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CAC1CJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDL,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDL,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDL,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,EACdL,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC9D,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCR,GAAG,CAACM,EAAE,CAAC,mBAAmB,CAAC,CAC5B,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCR,GAAG,CAACM,EAAE,CAAC,uBAAuB,CAAC,CAChC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MACLE,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFL,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,yBAAyB,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC5D,CAAC,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCJ,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACpDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDN,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,EACXL,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACR,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CACrC,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDP,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}