{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hot\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h3\", [_vm._v(\"热门品牌\")]), _c(\"p\", [_vm._v(\"国际经典 品质认证\")])]), _c(\"div\", {\n    staticClass: \"button\"\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-left-bold\"\n  })]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]), _c(\"div\", {\n    staticClass: \"bd\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot1.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot2.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot3.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot4.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot5.png\"),\n      alt: \"\"\n    }\n  })])])])])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "_v", "attrs", "href", "src", "require", "alt", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/XtxHotBrand.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"hot\" }, [\n      _c(\"div\", { staticClass: \"wrapper\" }, [\n        _c(\"div\", { staticClass: \"title\" }, [\n          _c(\"div\", { staticClass: \"left\" }, [\n            _c(\"h3\", [_vm._v(\"热门品牌\")]),\n            _c(\"p\", [_vm._v(\"国际经典 品质认证\")]),\n          ]),\n          _c(\"div\", { staticClass: \"button\" }, [\n            _c(\"a\", { attrs: { href: \"#\" } }, [\n              _c(\"i\", { staticClass: \"iconfont icon-arrow-left-bold\" }),\n            ]),\n            _c(\"a\", { attrs: { href: \"#\" } }, [\n              _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"bd\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: { src: require(\"@/assets/images/hot1.png\"), alt: \"\" },\n                }),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: { src: require(\"@/assets/images/hot2.png\"), alt: \"\" },\n                }),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: { src: require(\"@/assets/images/hot3.png\"), alt: \"\" },\n                }),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: { src: require(\"@/assets/images/hot4.png\"), alt: \"\" },\n                }),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: { src: require(\"@/assets/images/hot5.png\"), alt: \"\" },\n                }),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CACvCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAgC,CAAC,CAAC,CAC1D,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAK,CAAC,EAAE,CAC/BJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,KAAK,EAAE;IACRM,KAAK,EAAE;MAAEE,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7D,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}