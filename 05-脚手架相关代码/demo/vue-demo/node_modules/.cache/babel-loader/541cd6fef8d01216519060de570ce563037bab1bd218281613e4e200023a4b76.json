{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxNewGoods.vue"], "sourcesContent": ["<template>\n     <!-- 新鲜好物 -->\n     <div class=\"goods wrapper\">\n      <div class=\"title\">\n        <div class=\"left\">\n          <h3>新鲜好物</h3>\n          <p>新鲜出炉 品质靠谱</p>\n        </div>\n        <div class=\"right\">\n          <a href=\"#\" class=\"more\"\n            >查看全部<span class=\"iconfont icon-arrow-right-bold\"></span\n          ></a>\n        </div>\n      </div>\n      <div class=\"bd\">\n        <ul>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods1.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>KN95级莫兰迪色防护口罩</h4>\n                <p>¥ <span>79</span></p>\n              </div>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n/* 新鲜好物 */\n.goods .bd ul {\n  display: flex;\n  justify-content: space-between;\n}\n.goods .bd li {\n  width: 304px;\n  height: 404px;\n  background-color: #EEF9F4;\n}\n.goods .bd li {\n  display: block;\n}\n.goods .bd li .pic {\n  width: 304px;\n  height: 304px;\n}\n.goods .bd li .txt {\n  text-align: center;\n}\n.goods .bd li h4 {\n  margin-top: 17px;\n  margin-bottom: 8px;\n  font-size: 20px;\n}\n.goods .bd li p {\n  font-size: 18px;\n  color: #AA2113;\n}\n.goods .bd li p span {\n  font-size: 22px;\n}\n\n\n</style>"], "mappings": "AA+BA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}