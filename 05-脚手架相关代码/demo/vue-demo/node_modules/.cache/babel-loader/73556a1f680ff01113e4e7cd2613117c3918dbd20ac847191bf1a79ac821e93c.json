{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxShortCut.vue"], "sourcesContent": ["<template>\n      <!-- 快捷链接  -->\n      <div class=\"shortcut\">\n      <div class=\"wrapper\">\n        <ul>\n          <li><a href=\"#\" class=\"login\">请先登录</a></li>\n          <li><a href=\"#\">免费注册</a></li>\n          <li><a href=\"#\">我的订单</a></li>\n          <li><a href=\"#\">会员中心</a></li>\n          <li><a href=\"#\">帮助中心</a></li>\n          <li><a href=\"#\">在线客服</a></li>\n          <li>\n            <a href=\"#\"\n              ><span class=\"iconfont icon-mobile-phone\"></span>手机版</a\n            >\n          </li>\n        </ul>\n      </div>\n    </div>\n\n\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n</style> "], "mappings": "AAwBA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}