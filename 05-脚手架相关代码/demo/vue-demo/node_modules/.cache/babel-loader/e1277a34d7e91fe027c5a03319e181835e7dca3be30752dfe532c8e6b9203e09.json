{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxHeaderNav.vue"], "sourcesContent": ["<template>\n    <!-- 头部导航  -->\n    <div class=\"header wrapper\">\n      <!-- logo -->\n      <div class=\"logo\">\n        <h1>\n          <a href=\"#\">小兔鲜儿</a>\n        </h1>\n      </div>\n      <!-- 导航 -->\n      <div class=\"nav\">\n        <ul>\n          <li><a href=\"#\">首页</a></li>\n          <li><a href=\"#\">生鲜</a></li>\n          <li><a href=\"#\">美食</a></li>\n          <li><a href=\"#\">餐厨</a></li>\n          <li><a href=\"#\">电器</a></li>\n          <li><a href=\"#\">居家</a></li>\n          <li><a href=\"#\">洗护</a></li>\n          <li><a href=\"#\">孕婴</a></li>\n          <li><a href=\"#\">服装</a></li>\n        </ul>\n      </div>\n      <!-- 搜索 -->\n      <div class=\"search\">\n        <span class=\"iconfont icon-search\"></span>\n        <input type=\"text\" placeholder=\"搜一搜\" />\n      </div>\n      <!-- 购物车 -->\n      <div class=\"cart\">\n        <span class=\"iconfont icon-cart-full\"></span>\n        <i>2</i>\n      </div>\n    </div>\n\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n/* 头部导航 */\n.header {\n  display: flex;\n  margin: 22px auto;\n}\n.header .logo {\n  margin-right: 40px;\n  width: 200px;\n  height: 88px;\n  background-color: pink;\n}\n.header .logo a {\n  display: block;\n  width: 200px;\n  height: 88px;\n  background-image: url(~@/assets/images/logo.png);\n  font-size: 0;\n}\n.header .nav {\n  margin-top: 33px;\n  margin-right: 27px;\n}\n.header .nav ul {\n  display: flex;\n}\n.header .nav li {\n  margin-right: 48px;\n}\n.header .nav a {\n  display: block;\n  height: 34px;\n}\n.header .nav a:hover {\n  border-bottom: 2px solid #5EB69C;\n}\n.header .search {\n  display: flex;\n  margin-right: 45px;\n  margin-top: 33px;\n  width: 170px;\n  height: 34px;\n  border-bottom: 2px solid #F4F4F4;\n}\n.header .search .icon-search {\n  margin-right: 8px;\n  font-size: 20px;\n  color: #999;\n}\n.header .search input {\n  flex: 1;\n}\n.header .search input::placeholder {\n  color: #ccc;\n}\n.header .cart {\n  position: relative;\n  margin-top: 33px;\n}\n.header .cart .icon-cart-full {\n  font-size: 24px;\n}\n.header .cart i {\n  position: absolute;\n  /* right: -5px; */\n  left: 15px;\n  top: 0;\n  padding: 0 5px;\n  height: 15px;\n  background-color: #E26237;\n  border-radius: 7px;\n  font-size: 12px;\n  color: #fffefe;\n  line-height: 15px;\n}\n\n</style>"], "mappings": "AAsCA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}