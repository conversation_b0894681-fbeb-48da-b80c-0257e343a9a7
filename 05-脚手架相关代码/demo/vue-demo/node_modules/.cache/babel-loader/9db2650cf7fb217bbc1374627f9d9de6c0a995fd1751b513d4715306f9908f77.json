{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxHotBrand.vue"], "sourcesContent": ["<template>\n     <!-- 轮播区域 -->\n     <div class=\"banner\">\n      <div class=\"wrapper\">\n        <!-- 图 -->\n        <ul class=\"pic\">\n          <li>\n            <a href=\"#\"><img src=\"@/assets/images/banner1.png\" alt=\"\" /></a>\n          </li>\n          <li>\n            <a href=\"#\"><img src=\"@/assets/images/banner1.png\" alt=\"\" /></a>\n          </li>\n        </ul>\n        <!-- 侧导航 -->\n        <div class=\"subnav\">\n          <ul>\n            <li>\n              <div>\n                <span><a href=\"#\">生鲜</a></span>\n                <span><a href=\"#\">水果</a><a href=\"#\">蔬菜</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">美食</a></span>\n                <span><a href=\"#\">面点</a><a href=\"#\">干果</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">餐厨</a></span>\n                <span><a href=\"#\">数码产品</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">电器</a></span>\n                <span\n                  ><a href=\"#\">床品</a><a href=\"#\">四件套</a\n                  ><a href=\"#\">被枕</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">居家</a></span>\n                <span\n                  ><a href=\"#\">奶粉</a><a href=\"#\">玩具</a\n                  ><a href=\"#\">辅食</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">洗护</a></span>\n                <span\n                  ><a href=\"#\">洗发</a><a href=\"#\">洗护</a\n                  ><a href=\"#\">美妆</a></span\n                >\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">孕婴</a></span>\n                <span><a href=\"#\">奶粉</a><a href=\"#\">玩具</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">服饰</a></span>\n                <span><a href=\"#\">女装</a><a href=\"#\">男装</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">杂货</a></span>\n                <span><a href=\"#\">户外</a><a href=\"#\">图书</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n            <li>\n              <div>\n                <span><a href=\"#\">品牌</a></span>\n                <span><a href=\"#\">品牌制造</a></span>\n              </div>\n              <i class=\"iconfont icon-arrow-right-bold\"></i>\n            </li>\n          </ul>\n        </div>\n        <!-- 指示器 -->\n        <ol>\n          <li class=\"current\"><i></i></li>\n          <li><i></i></li>\n          <li><i></i></li>\n        </ol>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n/* 轮播区域 */\n.banner {\n  height: 500px;\n  background-color: #F5F5F5 ;\n}\n.banner .wrapper {\n  position: relative;\n  overflow: hidden;\n}\n.banner .pic {\n  display: flex;\n  width: 3720px;\n  height: 500px;\n}\n.banner .pic li {\n  width: 1240px;\n  height: 500px;\n}\n.banner .subnav {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 250px;\n  height: 500px;\n  background-color: rgba(0,0,0,0.42);\n}\n.banner .subnav li {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 20px 0 30px;\n  height: 50px;\n  line-height: 50px;\n}\n.banner .subnav a,\n.banner .subnav i {\n  color: #fff;\n}\n.banner .subnav li span:nth-child(1) {\n  margin-right: 14px;\n}\n.banner .subnav li span:nth-child(2) a {\n  margin-right: 5px;\n}\n.banner .subnav li span:nth-child(2) a {\n  font-size: 14px;\n}\n.banner .subnav li:hover {\n  background-color: #00BE9A;\n}\n.banner ol {\n  position: absolute;\n  right: 17px;\n  bottom: 17px;\n  display: flex;\n}\n.banner ol li {\n  cursor: pointer;\n  margin-left: 8px;\n  padding: 4px;\n  width: 22px;\n  height: 22px;\n  background-color: transparent;\n  border-radius: 50%;\n}\n.banner ol li i {\n  display: block;\n  width: 14px;\n  height: 14px;\n  background-color: rgba(255,255,255,0.5);\n  border-radius: 50%;\n}\n.banner ol .current {\n  background-color: rgba(255,255,255,0.5);\n}\n.banner ol .current i {\n  background-color: #fff;\n}\n\n</style>"], "mappings": "AA4GA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}