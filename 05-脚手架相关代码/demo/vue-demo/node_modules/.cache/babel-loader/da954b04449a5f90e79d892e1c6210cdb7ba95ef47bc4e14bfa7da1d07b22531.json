{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/BaseGoodItem.vue"], "sourcesContent": ["<template>\n  <li class=\"base-goods-item\">\n            <a href=\"#\">\n              <div class=\"pic\"><img src=\"@/assets/images/goods1.png\" alt=\"\" /></div>\n              <div class=\"txt\">\n                <h4>KN95级莫兰迪色防护口罩</h4>\n                <p>¥ <span>79</span></p>\n              </div>\n            </a>\n          </li>\n\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n.base-goods-item li {\n  width: 304px;\n  height: 404px;\n  background-color: #EEF9F4;\n}\n.base-goods-item li {\n  display: block;\n}\n.base-goods-item li .pic {\n  width: 304px;\n  height: 304px;\n}\n.base-goods-item li .txt {\n  text-align: center;\n}\n.base-goods-item li h4 {\n  margin-top: 17px;\n  margin-bottom: 8px;\n  font-size: 20px;\n}\n.base-goods-item li p {\n  font-size: 18px;\n  color: #AA2113;\n}\n.base-goods-item li p span {\n  font-size: 22px;\n}\n\n</style>"], "mappings": "AAcA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}