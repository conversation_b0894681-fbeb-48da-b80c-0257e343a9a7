{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxNewGoods.vue"], "sourcesContent": ["<template>\n     <!-- 新鲜好物 -->\n     <div class=\"goods wrapper\">\n      <div class=\"title\">\n        <div class=\"left\">\n          <h3>新鲜好物</h3>\n          <p>新鲜出炉 品质靠谱</p>\n        </div>\n        <div class=\"right\">\n          <a href=\"#\" class=\"more\"\n            >查看全部<span class=\"iconfont icon-arrow-right-bold\"></span\n          ></a>\n        </div>\n      </div>\n      <div class=\"bd\">\n        <ul>\n            <BaseGoodItem></BaseGoodItem>\n        </ul>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n\n/* 新鲜好物 */\n.goods .bd ul {\n  display: flex;\n  justify-content: space-between;\n}\n\n\n\n</style>"], "mappings": "AAuBA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}