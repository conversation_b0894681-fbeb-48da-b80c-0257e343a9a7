{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"shortcut\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    staticClass: \"login\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"请先登录\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"免费注册\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"我的订单\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"会员中心\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"帮助中心\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"在线客服\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-mobile-phone\"\n  }), _vm._v(\"手机版\")])])])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "attrs", "href", "_v", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/XtxShortCut.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"shortcut\" }, [\n      _c(\"div\", { staticClass: \"wrapper\" }, [\n        _c(\"ul\", [\n          _c(\"li\", [\n            _c(\"a\", { staticClass: \"login\", attrs: { href: \"#\" } }, [\n              _vm._v(\"请先登录\"),\n            ]),\n          ]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"免费注册\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"我的订单\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"会员中心\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"帮助中心\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"在线客服\")])]),\n          _c(\"li\", [\n            _c(\"a\", { attrs: { href: \"#\" } }, [\n              _c(\"span\", { staticClass: \"iconfont icon-mobile-phone\" }),\n              _vm._v(\"手机版\"),\n            ]),\n          ]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAW,CAAC,EAAE,CAC5CJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE,OAAO;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACtDP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAA6B,CAAC,CAAC,EACzDL,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDT,MAAM,CAACU,aAAa,GAAG,IAAI;AAE3B,SAASV,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}