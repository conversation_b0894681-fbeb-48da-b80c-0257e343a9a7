{"ast": null, "code": "export default {};", "map": {"version": 3, "names": [], "sources": ["src/components/XtxTopic.vue"], "sourcesContent": ["<template>\n    <!-- 最新专题 -->\n    <div class=\"topic wrapper\">\n      <div class=\"title\">\n        <div class=\"left\">\n          <h3>最新专题</h3>\n        </div>\n        <div class=\"right\">\n          <a href=\"#\" class=\"more\"\n            >查看全部<span class=\"iconfont icon-arrow-right-bold\"></span\n          ></a>\n        </div>\n      </div>\n      <div class=\"topic_bd\">\n        <ul>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\">\n                <img src=\"@/assets/images/topic1.png\" alt=\"\" />\n                <div class=\"info\">\n                  <div class=\"left\">\n                    <h5>吃这些美食才不算辜负自己</h5>\n                    <p>餐厨起居洗护好物</p>\n                  </div>\n                  <div class=\"right\">¥<span>29.9</span>起</div>\n                </div>\n              </div>\n              <div class=\"txt\">\n                <div class=\"left\">\n                  <p>\n                    <span class=\"iconfont icon-favorites-fill red\"></span>\n                    <i>1200</i>\n                  </p>\n                  <p>\n                    <span class=\"iconfont icon-browse\"></span>\n                    <i>1800</i>\n                  </p>\n                </div>\n                <div class=\"right\">\n                  <span class=\"iconfont icon-comment\"></span>\n                  <i>246</i>\n                </div>\n              </div>\n            </a>\n          </li>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\">\n                <img src=\"@/assets/images/topic2.png\" alt=\"\" />\n                <div class=\"info\">\n                  <div class=\"left\">\n                    <h5>吃这些美食才不算辜负自己</h5>\n                    <p>餐厨起居洗护好物</p>\n                  </div>\n                  <div class=\"right\">¥<span>29.9</span>起</div>\n                </div>\n              </div>\n              <div class=\"txt\">\n                <div class=\"left\">\n                  <p>\n                    <span class=\"iconfont icon-fabulous\"></span>\n                    <i>1200</i>\n                  </p>\n                  <p>\n                    <span class=\"iconfont icon-browse\"></span>\n                    <i>1800</i>\n                  </p>\n                </div>\n                <div class=\"right\">\n                  <span class=\"iconfont icon-comment\"></span>\n                  <i>246</i>\n                </div>\n              </div>\n            </a>\n          </li>\n          <li>\n            <a href=\"#\">\n              <div class=\"pic\">\n                <img src=\"@/assets/images/topic3.png\" alt=\"\" />\n                <div class=\"info\">\n                  <div class=\"left\">\n                    <h5>吃这些美食才不算辜负自己</h5>\n                    <p>餐厨起居洗护好物</p>\n                  </div>\n                  <div class=\"right\">¥<span>29.9</span>起</div>\n                </div>\n              </div>\n              <div class=\"txt\">\n                <div class=\"left\">\n                  <p>\n                    <span class=\"iconfont icon-fabulous\"></span>\n                    <i>1200</i>\n                  </p>\n                  <p>\n                    <span class=\"iconfont icon-browse\"></span>\n                    <i>1800</i>\n                  </p>\n                </div>\n                <div class=\"right\">\n                  <span class=\"iconfont icon-comment\"></span>\n                  <i>246</i>\n                </div>\n              </div>\n            </a>\n          </li>\n        </ul>\n      </div>\n    </div>\n</template>\n\n<script>\nexport default {\n\n}\n</script>\n\n<style>\n/* 最新专题 */\n.topic {\n  padding-top: 60px;\n  margin-bottom: 40px;\n}\n.topic_bd ul {\n  display: flex;\n  justify-content: space-between;\n}\n.topic_bd li {\n  width: 405px;\n  height: 355px;\n}\n.topic_bd .pic {\n  position: relative;\n  width: 405px;\n  height: 288px;\n}\n.topic_bd .txt {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 15px;\n  height: 67px;\n  line-height: 67px;\n  color: #666;\n  font-size: 14px;\n}\n.topic_bd .txt .left {\n  display: flex;\n}\n.topic_bd .txt .left p {\n  margin-right: 20px;\n}\n.topic_bd .txt .left .red {\n  color: #AA2113;\n}\n.topic_bd .info {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 15px;\n  width: 100%;\n  height: 90px;\n  background-image: linear-gradient(180deg, rgba(137,137,137,0.00) 0%, rgba(0,0,0,0.90) 100%);\n}\n.topic_bd .info .left {\n  padding-top: 20px;\n  color: #fff;\n}\n.topic_bd .info .left h5 {\n  margin-bottom: 5px;\n  font-size: 20px;\n}\n.topic_bd .info .right {\n  margin-top: 35px;\n  padding: 0 7px;\n  height: 25px;\n  line-height: 25px;\n  background-color: #fff;\n  color: #AA2113;\n  font-size: 15px;\n}\n\n</style>"], "mappings": "AA+GA,gBAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}