{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"header wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"logo\"\n  }, [_c(\"h1\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"小兔鲜儿\")])])]), _c(\"div\", {\n    staticClass: \"nav\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"首页\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"生鲜\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"美食\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"餐厨\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"电器\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"居家\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"洗护\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"孕婴\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"服装\")])])])]), _c(\"div\", {\n    staticClass: \"search\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-search\"\n  }), _c(\"input\", {\n    attrs: {\n      type: \"text\",\n      placeholder: \"搜一搜\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"cart\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-cart-full\"\n  }), _c(\"i\", [_vm._v(\"2\")])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "attrs", "href", "_v", "type", "placeholder", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/XtxHeaderNav.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header wrapper\" }, [\n      _c(\"div\", { staticClass: \"logo\" }, [\n        _c(\"h1\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"小兔鲜儿\")])]),\n      ]),\n      _c(\"div\", { staticClass: \"nav\" }, [\n        _c(\"ul\", [\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"首页\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"生鲜\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"美食\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"餐厨\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"电器\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"居家\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"洗护\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"孕婴\")])]),\n          _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"服装\")])]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"search\" }, [\n        _c(\"span\", { staticClass: \"iconfont icon-search\" }),\n        _c(\"input\", { attrs: { type: \"text\", placeholder: \"搜一搜\" } }),\n      ]),\n      _c(\"div\", { staticClass: \"cart\" }, [\n        _c(\"span\", { staticClass: \"iconfont icon-cart-full\" }),\n        _c(\"i\", [_vm._v(\"2\")]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,CAAC,EACnDJ,EAAE,CAAC,OAAO,EAAE;IAAEK,KAAK,EAAE;MAAEG,IAAI,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAM;EAAE,CAAC,CAAC,CAC7D,CAAC,EACFT,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAA0B,CAAC,CAAC,EACtDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDT,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}