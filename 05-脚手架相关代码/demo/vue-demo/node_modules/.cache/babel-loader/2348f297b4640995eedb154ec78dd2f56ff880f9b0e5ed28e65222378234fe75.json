{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hot\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"bd\"\n  }, [_c(\"ul\", [_c(\"BaseBrandItem\"), _c(\"BaseBrandItem\"), _c(\"BaseBrandItem\"), _c(\"BaseBrandItem\"), _c(\"BaseBrandItem\")], 1)])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h3\", [_vm._v(\"热门品牌\")]), _c(\"p\", [_vm._v(\"国际经典 品质认证\")])]), _c(\"div\", {\n    staticClass: \"button\"\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-left-bold\"\n  })]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "staticRenderFns", "_v", "attrs", "href", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/components/XtxHotBrand.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"hot\" }, [\n    _c(\"div\", { staticClass: \"wrapper\" }, [\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"bd\" }, [\n        _c(\n          \"ul\",\n          [\n            _c(\"BaseBrandItem\"),\n            _c(\"BaseBrandItem\"),\n            _c(\"BaseBrandItem\"),\n            _c(\"BaseBrandItem\"),\n            _c(\"BaseBrandItem\"),\n          ],\n          1\n        ),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"title\" }, [\n      _c(\"div\", { staticClass: \"left\" }, [\n        _c(\"h3\", [_vm._v(\"热门品牌\")]),\n        _c(\"p\", [_vm._v(\"国际经典 品质认证\")]),\n      ]),\n      _c(\"div\", { staticClass: \"button\" }, [\n        _c(\"a\", { attrs: { href: \"#\" } }, [\n          _c(\"i\", { staticClass: \"iconfont icon-arrow-left-bold\" }),\n        ]),\n        _c(\"a\", { attrs: { href: \"#\" } }, [\n          _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAM,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAK,CAAC,EAAE,CAC/BF,EAAE,CACA,IAAI,EACJ,CACEA,EAAE,CAAC,eAAe,CAAC,EACnBA,EAAE,CAAC,eAAe,CAAC,EACnBA,EAAE,CAAC,eAAe,CAAC,EACnBA,EAAE,CAAC,eAAe,CAAC,EACnBA,EAAE,CAAC,eAAe,CAAC,CACpB,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAII,eAAe,GAAG,CACpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BL,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCF,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgC,CAAC,CAAC,CAC1D,CAAC,EACFF,EAAE,CAAC,GAAG,EAAE;IAAEM,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDJ,MAAM,CAACU,aAAa,GAAG,IAAI;AAE3B,SAASV,MAAM,EAAEM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}