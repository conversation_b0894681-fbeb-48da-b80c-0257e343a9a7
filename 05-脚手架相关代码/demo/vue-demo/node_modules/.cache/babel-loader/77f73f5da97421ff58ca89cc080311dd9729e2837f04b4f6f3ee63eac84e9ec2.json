{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"dev\", {\n    staticClass: \"App\"\n  }, [_vm._v(\" 我是首页 \"), _c(\"XtxShortCut\"), _c(\"XtxHeaderNav\"), _c(\"XtxBanner\"), _c(\"XtxNewGoods\"), _c(\"XtxHotBrand\"), _c(\"XtxTopic\"), _c(\"XtxFooter\")], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"dev\",\n    { staticClass: \"App\" },\n    [\n      _vm._v(\" 我是首页 \"),\n      _c(\"XtxShortCut\"),\n      _c(\"XtxHeaderNav\"),\n      _c(\"XtxBanner\"),\n      _c(\"XtxNewGoods\"),\n      _c(\"XtxHotBrand\"),\n      _c(\"XtxTopic\"),\n      _c(\"XtxFooter\"),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAM,CAAC,EACtB,CACEH,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,EAChBH,EAAE,CAAC,aAAa,CAAC,EACjBA,EAAE,CAAC,cAAc,CAAC,EAClBA,EAAE,CAAC,WAAW,CAAC,EACfA,EAAE,CAAC,aAAa,CAAC,EACjBA,EAAE,CAAC,aAAa,CAAC,EACjBA,EAAE,CAAC,UAAU,CAAC,EACdA,EAAE,CAAC,WAAW,CAAC,CAChB,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxBN,MAAM,CAACO,aAAa,GAAG,IAAI;AAE3B,SAASP,MAAM,EAAEM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}