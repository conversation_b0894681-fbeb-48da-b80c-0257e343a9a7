{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _vm._m(0);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"App\"\n  }, [_c(\"div\", {\n    staticClass: \"shortcut\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    staticClass: \"login\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"请先登录\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"免费注册\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"我的订单\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"会员中心\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"帮助中心\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"在线客服\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-mobile-phone\"\n  }), _vm._v(\"手机版\")])])])])]), _c(\"div\", {\n    staticClass: \"header wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"logo\"\n  }, [_c(\"h1\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"小兔鲜儿\")])])]), _c(\"div\", {\n    staticClass: \"nav\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"首页\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"生鲜\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"美食\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"餐厨\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"电器\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"居家\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"洗护\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"孕婴\")])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"服装\")])])])]), _c(\"div\", {\n    staticClass: \"search\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-search\"\n  }), _c(\"input\", {\n    attrs: {\n      type: \"text\",\n      placeholder: \"搜一搜\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"cart\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-cart-full\"\n  }), _c(\"i\", [_vm._v(\"2\")])])]), _c(\"div\", {\n    staticClass: \"banner\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"ul\", {\n    staticClass: \"pic\"\n  }, [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/banner1.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/banner1.png\"),\n      alt: \"\"\n    }\n  })])])]), _c(\"div\", {\n    staticClass: \"subnav\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"生鲜\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"水果\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"蔬菜\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"美食\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"面点\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"干果\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"餐厨\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"数码产品\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"电器\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"床品\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"四件套\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"被枕\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"居家\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"奶粉\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"玩具\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"辅食\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"洗护\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"洗发\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"洗护\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"美妆\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"孕婴\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"奶粉\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"玩具\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"服饰\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"女装\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"男装\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"杂货\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"户外\")]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"图书\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })]), _c(\"li\", [_c(\"div\", [_c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"品牌\")])]), _c(\"span\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"品牌制造\")])])]), _c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]), _c(\"ol\", [_c(\"li\", {\n    staticClass: \"current\"\n  }, [_c(\"i\")]), _c(\"li\", [_c(\"i\")]), _c(\"li\", [_c(\"i\")])])])]), _c(\"div\", {\n    staticClass: \"goods wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h3\", [_vm._v(\"新鲜好物\")]), _c(\"p\", [_vm._v(\"新鲜出炉 品质靠谱\")])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"a\", {\n    staticClass: \"more\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"查看全部\"), _c(\"span\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]), _c(\"div\", {\n    staticClass: \"bd\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/goods1.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]), _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"79\")])])])])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/goods2.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]), _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"566\")])])])])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/goods3.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"h4\", [_vm._v(\"法拉蒙高颜值记事本可定制\")]), _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"58\")])])])])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/goods4.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"h4\", [_vm._v(\"科技布布艺沙发\")]), _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"3759\")])])])])])])])]), _c(\"div\", {\n    staticClass: \"hot\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h3\", [_vm._v(\"热门品牌\")]), _c(\"p\", [_vm._v(\"国际经典 品质认证\")])]), _c(\"div\", {\n    staticClass: \"button\"\n  }, [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-left-bold\"\n  })]), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]), _c(\"div\", {\n    staticClass: \"bd\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot1.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot2.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot3.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot4.png\"),\n      alt: \"\"\n    }\n  })])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/hot5.png\"),\n      alt: \"\"\n    }\n  })])])])])])]), _c(\"div\", {\n    staticClass: \"topic wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"title\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h3\", [_vm._v(\"最新专题\")])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"a\", {\n    staticClass: \"more\",\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"查看全部\"), _c(\"span\", {\n    staticClass: \"iconfont icon-arrow-right-bold\"\n  })])])]), _c(\"div\", {\n    staticClass: \"topic_bd\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/topic1.png\"),\n      alt: \"\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"info\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]), _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_vm._v(\"¥\"), _c(\"span\", [_vm._v(\"29.9\")]), _vm._v(\"起\")])])]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-favorites-fill red\"\n  }), _c(\"i\", [_vm._v(\"1200\")])]), _c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-browse\"\n  }), _c(\"i\", [_vm._v(\"1800\")])])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-comment\"\n  }), _c(\"i\", [_vm._v(\"246\")])])])])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/topic2.png\"),\n      alt: \"\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"info\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]), _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_vm._v(\"¥\"), _c(\"span\", [_vm._v(\"29.9\")]), _vm._v(\"起\")])])]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-fabulous\"\n  }), _c(\"i\", [_vm._v(\"1200\")])]), _c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-browse\"\n  }), _c(\"i\", [_vm._v(\"1800\")])])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-comment\"\n  }), _c(\"i\", [_vm._v(\"246\")])])])])]), _c(\"li\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_c(\"div\", {\n    staticClass: \"pic\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/topic3.png\"),\n      alt: \"\"\n    }\n  }), _c(\"div\", {\n    staticClass: \"info\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]), _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_vm._v(\"¥\"), _c(\"span\", [_vm._v(\"29.9\")]), _vm._v(\"起\")])])]), _c(\"div\", {\n    staticClass: \"txt\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-fabulous\"\n  }), _c(\"i\", [_vm._v(\"1200\")])]), _c(\"p\", [_c(\"span\", {\n    staticClass: \"iconfont icon-browse\"\n  }), _c(\"i\", [_vm._v(\"1800\")])])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"span\", {\n    staticClass: \"iconfont icon-comment\"\n  }), _c(\"i\", [_vm._v(\"246\")])])])])])])])]), _c(\"div\", {\n    staticClass: \"footer\"\n  }, [_c(\"div\", {\n    staticClass: \"wrapper\"\n  }, [_c(\"div\", {\n    staticClass: \"service\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"价格亲民\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"物流快捷\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"品质新鲜\")])]), _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"售后无忧\")])])])]), _c(\"div\", {\n    staticClass: \"help\"\n  }, [_c(\"div\", {\n    staticClass: \"left\"\n  }, [_c(\"dl\", [_c(\"dt\", [_vm._v(\"购物指南\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"购物流程\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"支付方式\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后规则\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"配送方式\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送运费\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送范围\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送时间\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"关于我们\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"平台规则\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"联系我们\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"问题反馈\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"售后服务\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后政策\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"退款说明\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"取消订单\")])])]), _c(\"dl\", [_c(\"dt\", [_vm._v(\"服务热线\")]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"在线客服\"), _c(\"span\", {\n    staticClass: \"iconfont icon-customer-service\"\n  })])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"客服电话 400-0000-000\")])]), _c(\"dd\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"工作时间 周一至周日 8:00-18:00\")])])])]), _c(\"div\", {\n    staticClass: \"right\"\n  }, [_c(\"ul\", [_c(\"li\", [_c(\"div\", [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/wechat.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"p\", [_vm._v(\"微信公众号\")])]), _c(\"li\", [_c(\"div\", [_c(\"img\", {\n    attrs: {\n      src: require(\"@/assets/images/app.png\"),\n      alt: \"\"\n    }\n  })]), _c(\"p\", [_vm._v(\"APP下载二维码\")])])])])]), _c(\"div\", {\n    staticClass: \"copyright\"\n  }, [_c(\"p\", [_c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"关于我们\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"帮助中心\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"售后服务\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"配送与验收\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"商务合作\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"搜索推荐\")]), _vm._v(\"|\"), _c(\"a\", {\n    attrs: {\n      href: \"#\"\n    }\n  }, [_vm._v(\"友情链接\")])]), _c(\"p\", [_vm._v(\"CopyRight © 小兔鲜\")])])])])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "_m", "staticRenderFns", "staticClass", "attrs", "href", "_v", "type", "placeholder", "src", "require", "alt", "_withStripped"], "sources": ["/Users/<USER>/Desktop/2、最新Vue2+3 入门到实战课程/Vue2+3入门到实战-配套资料/01-随堂代码&素材/day03/准备代码/05-脚手架相关代码/demo/vue-demo/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm._m(0)\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"App\" }, [\n      _c(\"div\", { staticClass: \"shortcut\" }, [\n        _c(\"div\", { staticClass: \"wrapper\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [\n              _c(\"a\", { staticClass: \"login\", attrs: { href: \"#\" } }, [\n                _vm._v(\"请先登录\"),\n              ]),\n            ]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"免费注册\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"我的订单\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"会员中心\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"帮助中心\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"在线客服\")])]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"span\", { staticClass: \"iconfont icon-mobile-phone\" }),\n                _vm._v(\"手机版\"),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"header wrapper\" }, [\n        _c(\"div\", { staticClass: \"logo\" }, [\n          _c(\"h1\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"小兔鲜儿\")])]),\n        ]),\n        _c(\"div\", { staticClass: \"nav\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"首页\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"生鲜\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"美食\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"餐厨\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"电器\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"居家\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"洗护\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"孕婴\")])]),\n            _c(\"li\", [_c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"服装\")])]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"search\" }, [\n          _c(\"span\", { staticClass: \"iconfont icon-search\" }),\n          _c(\"input\", { attrs: { type: \"text\", placeholder: \"搜一搜\" } }),\n        ]),\n        _c(\"div\", { staticClass: \"cart\" }, [\n          _c(\"span\", { staticClass: \"iconfont icon-cart-full\" }),\n          _c(\"i\", [_vm._v(\"2\")]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"banner\" }, [\n        _c(\"div\", { staticClass: \"wrapper\" }, [\n          _c(\"ul\", { staticClass: \"pic\" }, [\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: require(\"@/assets/images/banner1.png\"),\n                    alt: \"\",\n                  },\n                }),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: require(\"@/assets/images/banner1.png\"),\n                    alt: \"\",\n                  },\n                }),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"subnav\" }, [\n            _c(\"ul\", [\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"生鲜\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"水果\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"蔬菜\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"美食\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"面点\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"干果\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"餐厨\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"数码产品\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"电器\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"床品\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"四件套\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"被枕\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"居家\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"奶粉\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"玩具\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"辅食\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"洗护\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"洗发\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"洗护\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"美妆\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"孕婴\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"奶粉\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"玩具\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"服饰\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"女装\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"男装\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"杂货\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"户外\")]),\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"图书\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n              _c(\"li\", [\n                _c(\"div\", [\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"品牌\")]),\n                  ]),\n                  _c(\"span\", [\n                    _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"品牌制造\")]),\n                  ]),\n                ]),\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n            ]),\n          ]),\n          _c(\"ol\", [\n            _c(\"li\", { staticClass: \"current\" }, [_c(\"i\")]),\n            _c(\"li\", [_c(\"i\")]),\n            _c(\"li\", [_c(\"i\")]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"goods wrapper\" }, [\n        _c(\"div\", { staticClass: \"title\" }, [\n          _c(\"div\", { staticClass: \"left\" }, [\n            _c(\"h3\", [_vm._v(\"新鲜好物\")]),\n            _c(\"p\", [_vm._v(\"新鲜出炉 品质靠谱\")]),\n          ]),\n          _c(\"div\", { staticClass: \"right\" }, [\n            _c(\"a\", { staticClass: \"more\", attrs: { href: \"#\" } }, [\n              _vm._v(\"查看全部\"),\n              _c(\"span\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"bd\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/goods1.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]),\n                  _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"79\")])]),\n                ]),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/goods2.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"h4\", [_vm._v(\"KN95级莫兰迪色防护口罩\")]),\n                  _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"566\")])]),\n                ]),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/goods3.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"h4\", [_vm._v(\"法拉蒙高颜值记事本可定制\")]),\n                  _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"58\")])]),\n                ]),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/goods4.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"h4\", [_vm._v(\"科技布布艺沙发\")]),\n                  _c(\"p\", [_vm._v(\"¥ \"), _c(\"span\", [_vm._v(\"3759\")])]),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"hot\" }, [\n        _c(\"div\", { staticClass: \"wrapper\" }, [\n          _c(\"div\", { staticClass: \"title\" }, [\n            _c(\"div\", { staticClass: \"left\" }, [\n              _c(\"h3\", [_vm._v(\"热门品牌\")]),\n              _c(\"p\", [_vm._v(\"国际经典 品质认证\")]),\n            ]),\n            _c(\"div\", { staticClass: \"button\" }, [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-left-bold\" }),\n              ]),\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"i\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"bd\" }, [\n            _c(\"ul\", [\n              _c(\"li\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/hot1.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n              ]),\n              _c(\"li\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/hot2.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n              ]),\n              _c(\"li\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/hot3.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n              ]),\n              _c(\"li\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/hot4.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n              ]),\n              _c(\"li\", [\n                _c(\"a\", { attrs: { href: \"#\" } }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/hot5.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"topic wrapper\" }, [\n        _c(\"div\", { staticClass: \"title\" }, [\n          _c(\"div\", { staticClass: \"left\" }, [_c(\"h3\", [_vm._v(\"最新专题\")])]),\n          _c(\"div\", { staticClass: \"right\" }, [\n            _c(\"a\", { staticClass: \"more\", attrs: { href: \"#\" } }, [\n              _vm._v(\"查看全部\"),\n              _c(\"span\", { staticClass: \"iconfont icon-arrow-right-bold\" }),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"topic_bd\" }, [\n          _c(\"ul\", [\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/topic1.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                  _c(\"div\", { staticClass: \"info\" }, [\n                    _c(\"div\", { staticClass: \"left\" }, [\n                      _c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]),\n                      _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")]),\n                    ]),\n                    _c(\"div\", { staticClass: \"right\" }, [\n                      _vm._v(\"¥\"),\n                      _c(\"span\", [_vm._v(\"29.9\")]),\n                      _vm._v(\"起\"),\n                    ]),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"div\", { staticClass: \"left\" }, [\n                    _c(\"p\", [\n                      _c(\"span\", {\n                        staticClass: \"iconfont icon-favorites-fill red\",\n                      }),\n                      _c(\"i\", [_vm._v(\"1200\")]),\n                    ]),\n                    _c(\"p\", [\n                      _c(\"span\", { staticClass: \"iconfont icon-browse\" }),\n                      _c(\"i\", [_vm._v(\"1800\")]),\n                    ]),\n                  ]),\n                  _c(\"div\", { staticClass: \"right\" }, [\n                    _c(\"span\", { staticClass: \"iconfont icon-comment\" }),\n                    _c(\"i\", [_vm._v(\"246\")]),\n                  ]),\n                ]),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/topic2.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                  _c(\"div\", { staticClass: \"info\" }, [\n                    _c(\"div\", { staticClass: \"left\" }, [\n                      _c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]),\n                      _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")]),\n                    ]),\n                    _c(\"div\", { staticClass: \"right\" }, [\n                      _vm._v(\"¥\"),\n                      _c(\"span\", [_vm._v(\"29.9\")]),\n                      _vm._v(\"起\"),\n                    ]),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"div\", { staticClass: \"left\" }, [\n                    _c(\"p\", [\n                      _c(\"span\", { staticClass: \"iconfont icon-fabulous\" }),\n                      _c(\"i\", [_vm._v(\"1200\")]),\n                    ]),\n                    _c(\"p\", [\n                      _c(\"span\", { staticClass: \"iconfont icon-browse\" }),\n                      _c(\"i\", [_vm._v(\"1800\")]),\n                    ]),\n                  ]),\n                  _c(\"div\", { staticClass: \"right\" }, [\n                    _c(\"span\", { staticClass: \"iconfont icon-comment\" }),\n                    _c(\"i\", [_vm._v(\"246\")]),\n                  ]),\n                ]),\n              ]),\n            ]),\n            _c(\"li\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [\n                _c(\"div\", { staticClass: \"pic\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: require(\"@/assets/images/topic3.png\"),\n                      alt: \"\",\n                    },\n                  }),\n                  _c(\"div\", { staticClass: \"info\" }, [\n                    _c(\"div\", { staticClass: \"left\" }, [\n                      _c(\"h5\", [_vm._v(\"吃这些美食才不算辜负自己\")]),\n                      _c(\"p\", [_vm._v(\"餐厨起居洗护好物\")]),\n                    ]),\n                    _c(\"div\", { staticClass: \"right\" }, [\n                      _vm._v(\"¥\"),\n                      _c(\"span\", [_vm._v(\"29.9\")]),\n                      _vm._v(\"起\"),\n                    ]),\n                  ]),\n                ]),\n                _c(\"div\", { staticClass: \"txt\" }, [\n                  _c(\"div\", { staticClass: \"left\" }, [\n                    _c(\"p\", [\n                      _c(\"span\", { staticClass: \"iconfont icon-fabulous\" }),\n                      _c(\"i\", [_vm._v(\"1200\")]),\n                    ]),\n                    _c(\"p\", [\n                      _c(\"span\", { staticClass: \"iconfont icon-browse\" }),\n                      _c(\"i\", [_vm._v(\"1800\")]),\n                    ]),\n                  ]),\n                  _c(\"div\", { staticClass: \"right\" }, [\n                    _c(\"span\", { staticClass: \"iconfont icon-comment\" }),\n                    _c(\"i\", [_vm._v(\"246\")]),\n                  ]),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"footer\" }, [\n        _c(\"div\", { staticClass: \"wrapper\" }, [\n          _c(\"div\", { staticClass: \"service\" }, [\n            _c(\"ul\", [\n              _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"价格亲民\")])]),\n              _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"物流快捷\")])]),\n              _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"品质新鲜\")])]),\n              _c(\"li\", [_c(\"span\"), _c(\"p\", [_vm._v(\"售后无忧\")])]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"help\" }, [\n            _c(\"div\", { staticClass: \"left\" }, [\n              _c(\"dl\", [\n                _c(\"dt\", [_vm._v(\"购物指南\")]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"购物流程\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"支付方式\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后规则\")]),\n                ]),\n              ]),\n              _c(\"dl\", [\n                _c(\"dt\", [_vm._v(\"配送方式\")]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送运费\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送范围\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送时间\")]),\n                ]),\n              ]),\n              _c(\"dl\", [\n                _c(\"dt\", [_vm._v(\"关于我们\")]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"平台规则\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"联系我们\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"问题反馈\")]),\n                ]),\n              ]),\n              _c(\"dl\", [\n                _c(\"dt\", [_vm._v(\"售后服务\")]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后政策\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"退款说明\")]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"取消订单\")]),\n                ]),\n              ]),\n              _c(\"dl\", [\n                _c(\"dt\", [_vm._v(\"服务热线\")]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [\n                    _vm._v(\"在线客服\"),\n                    _c(\"span\", {\n                      staticClass: \"iconfont icon-customer-service\",\n                    }),\n                  ]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [\n                    _vm._v(\"客服电话 400-0000-000\"),\n                  ]),\n                ]),\n                _c(\"dd\", [\n                  _c(\"a\", { attrs: { href: \"#\" } }, [\n                    _vm._v(\"工作时间 周一至周日 8:00-18:00\"),\n                  ]),\n                ]),\n              ]),\n            ]),\n            _c(\"div\", { staticClass: \"right\" }, [\n              _c(\"ul\", [\n                _c(\"li\", [\n                  _c(\"div\", [\n                    _c(\"img\", {\n                      attrs: {\n                        src: require(\"@/assets/images/wechat.png\"),\n                        alt: \"\",\n                      },\n                    }),\n                  ]),\n                  _c(\"p\", [_vm._v(\"微信公众号\")]),\n                ]),\n                _c(\"li\", [\n                  _c(\"div\", [\n                    _c(\"img\", {\n                      attrs: {\n                        src: require(\"@/assets/images/app.png\"),\n                        alt: \"\",\n                      },\n                    }),\n                  ]),\n                  _c(\"p\", [_vm._v(\"APP下载二维码\")]),\n                ]),\n              ]),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"copyright\" }, [\n            _c(\"p\", [\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"关于我们\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"帮助中心\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"售后服务\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"配送与验收\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"商务合作\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"搜索推荐\")]),\n              _vm._v(\"|\"),\n              _c(\"a\", { attrs: { href: \"#\" } }, [_vm._v(\"友情链接\")]),\n            ]),\n            _c(\"p\", [_vm._v(\"CopyRight © 小兔鲜\")]),\n          ]),\n        ]),\n      ]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CACvCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE,OAAO;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACtDP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/DP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAA6B,CAAC,CAAC,EACzDL,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CACd,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7DP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,CAAC,EACnDJ,EAAE,CAAC,OAAO,EAAE;IAAEK,KAAK,EAAE;MAAEG,IAAI,EAAE,MAAM;MAAEC,WAAW,EAAE;IAAM;EAAE,CAAC,CAAC,CAC7D,CAAC,EACFT,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAA0B,CAAC,CAAC,EACtDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACvB,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAC/BJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAC;MAC3CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,6BAA6B,CAAC;MAC3CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAClDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,EACFP,EAAE,CAAC,MAAM,EAAE,CACTA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CAACJ,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAC/CA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EACnBA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CACpB,CAAC,CACH,CAAC,CACH,CAAC,EACFA,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACrDP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,EACdP,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC9D,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAK,CAAC,EAAE,CAC/BJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,EACnCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,EAAEP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,EACnCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,EAAEP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACrD,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,EAAEP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC7BP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,IAAI,CAAC,EAAEP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CACtD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAgC,CAAC,CAAC,CAC1D,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAK,CAAC,EAAE,CAC/BJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,0BAA0B,CAAC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CAACJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAChEP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,GAAG,EAAE;IAAEI,WAAW,EAAE,MAAM;IAAEC,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CACrDP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,EACdP,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAiC,CAAC,CAAC,CAC9D,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCL,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IACTI,WAAW,EAAE;EACf,CAAC,CAAC,EACFJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,CAAC,EACnDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAwB,CAAC,CAAC,EACpDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CACzB,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCL,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAyB,CAAC,CAAC,EACrDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,CAAC,EACnDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAwB,CAAC,CAAC,EACpDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CACzB,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCN,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCL,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,CACZ,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAM,CAAC,EAAE,CAChCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAyB,CAAC,CAAC,EACrDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAuB,CAAC,CAAC,EACnDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC1B,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,MAAM,EAAE;IAAEI,WAAW,EAAE;EAAwB,CAAC,CAAC,EACpDJ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CACzB,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAS,CAAC,EAAE,CACnCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EACjDP,EAAE,CAAC,IAAI,EAAE,CAACA,EAAE,CAAC,MAAM,CAAC,EAAEA,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAO,CAAC,EAAE,CACjCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,EACdP,EAAE,CAAC,MAAM,EAAE;IACTI,WAAW,EAAE;EACf,CAAC,CAAC,CACH,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,GAAG,CAACQ,EAAE,CAAC,mBAAmB,CAAC,CAC5B,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAChCP,GAAG,CAACQ,EAAE,CAAC,uBAAuB,CAAC,CAChC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAQ,CAAC,EAAE,CAClCJ,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAC1CC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFP,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,KAAK,EAAE,CACRA,EAAE,CAAC,KAAK,EAAE;IACRK,KAAK,EAAE;MACLK,GAAG,EAAEC,OAAO,CAAC,yBAAyB,CAAC;MACvCC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC9B,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFP,EAAE,CAAC,KAAK,EAAE;IAAEI,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCJ,EAAE,CAAC,GAAG,EAAE,CACNA,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EACpDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACnDR,GAAG,CAACQ,EAAE,CAAC,GAAG,CAAC,EACXP,EAAE,CAAC,GAAG,EAAE;IAAEK,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAI;EAAE,CAAC,EAAE,CAACP,GAAG,CAACQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACpD,CAAC,EACFP,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACQ,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CACrC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC,CACF;AACDT,MAAM,CAACe,aAAa,GAAG,IAAI;AAE3B,SAASf,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}