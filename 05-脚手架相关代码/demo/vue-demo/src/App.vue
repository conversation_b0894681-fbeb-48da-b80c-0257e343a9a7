<template>
  <dev class="App">
    <!-- XtxShortCut  => 快捷链接

        XtxHeaderNav => 顶部导航
        XtxBanner    => 轮播区域
        XtxNewGoods  => 新鲜好物
        XtxHotBrand  => 热门品牌
        XtxTopic     => 最新专题
        XtxFooter    => 版权底部 
        
        -->
        <XtxShortCut></XtxShortCut>
        <XtxHeaderNav></XtxHeaderNav>
        <XtxBanner></XtxBanner>
        <XtxNewGoods></XtxNewGoods>
        <XtxHotBrand></XtxHotBrand>
        <XtxTopic></XtxTopic>
        <XtxFooter></XtxFooter>
        


      

  </dev>
</template>

<script>
import XtxShortCut from './components/XtxShortCut.vue'
import XtxHeaderNav from './components/XtxHeaderNav.vue'
import XtxBanner from './components/XtxBanner.vue'
import XtxNewGoods from './components/XtxNewGoods.vue'
import XtxHotBrand from './components/XtxHotBrand.vue'
import XtxTopic from './components/XtxTopic.vue'
import XtxFooter from './components/XtxFooter.vue'

export default {

  components: {
    XtxShortCut
    ,XtxHeaderNav
    ,XtxBanner
    ,XtxNewGoods
    ,XtxHotBrand
    ,XtxTopic
    ,XtxFooter

  }

}
</script>

<style>

</style>