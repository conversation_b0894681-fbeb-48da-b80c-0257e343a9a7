/* 公共的全局样式 */
.wrapper {
  margin: 0 auto;
  width: 1240px;
}

.title {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  margin-bottom: 30px;
  height: 42px;
}
.title .left {
  display: flex;
  align-items: flex-end;
}
.title .left h3 {
  margin-right: 35px;
  font-size: 30px;
}
.title .left p {
  padding-bottom: 5px;
  color: #A1A1A1;
}
.title .right {
  line-height: 42px;
}
.title .right .more {
  color: #A1A1A1;
}
.title .right .iconfont {
  margin-left: 10px;
}
