/* 快捷导航 */
.shortcut {
  height: 52px;
  line-height: 52px;
  background-color: #333;
}
.shortcut .wrapper {
  display: flex;
  justify-content: flex-end;
}
.shortcut ul {
  display: flex;
}
.shortcut a {
  padding: 0 15px;
  border-right: 1px solid #999;
  color: #fff;
  font-size: 14px;
  line-height: 14px;
}
.shortcut .login {
  color: #5EB69C;
}
.shortcut .icon-mobile-phone {
  margin-right: 5px;
}

/* 头部导航 */
.header {
  display: flex;
  margin: 22px auto;
}
.header .logo {
  margin-right: 40px;
  width: 200px;
  height: 88px;
  background-color: pink;
}
.header .logo a {
  display: block;
  width: 200px;
  height: 88px;
  background-image: url(~@/assets/images/logo.png);
  font-size: 0;
}
.header .nav {
  margin-top: 33px;
  margin-right: 27px;
}
.header .nav ul {
  display: flex;
}
.header .nav li {
  margin-right: 48px;
}
.header .nav a {
  display: block;
  height: 34px;
}
.header .nav a:hover {
  border-bottom: 2px solid #5EB69C;
}
.header .search {
  display: flex;
  margin-right: 45px;
  margin-top: 33px;
  width: 170px;
  height: 34px;
  border-bottom: 2px solid #F4F4F4;
}
.header .search .icon-search {
  margin-right: 8px;
  font-size: 20px;
  color: #999;
}
.header .search input {
  flex: 1;
}
.header .search input::placeholder {
  color: #ccc;
}
.header .cart {
  position: relative;
  margin-top: 33px;
}
.header .cart .icon-cart-full {
  font-size: 24px;
}
.header .cart i {
  position: absolute;
  /* right: -5px; */
  left: 15px;
  top: 0;
  padding: 0 5px;
  height: 15px;
  background-color: #E26237;
  border-radius: 7px;
  font-size: 12px;
  color: #fffefe;
  line-height: 15px;
}


/* 轮播区域 */
.banner {
  height: 500px;
  background-color: #F5F5F5 ;
}
.banner .wrapper {
  position: relative;
  overflow: hidden;
}
.banner .pic {
  display: flex;
  width: 3720px;
  height: 500px;
}
.banner .pic li {
  width: 1240px;
  height: 500px;
}
.banner .subnav {
  position: absolute;
  left: 0;
  top: 0;
  width: 250px;
  height: 500px;
  background-color: rgba(0,0,0,0.42);
}
.banner .subnav li {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 0 30px;
  height: 50px;
  line-height: 50px;
}
.banner .subnav a,
.banner .subnav i {
  color: #fff;
}
.banner .subnav li span:nth-child(1) {
  margin-right: 14px;
}
.banner .subnav li span:nth-child(2) a {
  margin-right: 5px;
}
.banner .subnav li span:nth-child(2) a {
  font-size: 14px;
}
.banner .subnav li:hover {
  background-color: #00BE9A;
}
.banner ol {
  position: absolute;
  right: 17px;
  bottom: 17px;
  display: flex;
}
.banner ol li {
  cursor: pointer;
  margin-left: 8px;
  padding: 4px;
  width: 22px;
  height: 22px;
  background-color: transparent;
  border-radius: 50%;
}
.banner ol li i {
  display: block;
  width: 14px;
  height: 14px;
  background-color: rgba(255,255,255,0.5);
  border-radius: 50%;
}
.banner ol .current {
  background-color: rgba(255,255,255,0.5);
}
.banner ol .current i {
  background-color: #fff;
}



/* 新鲜好物 */
.goods .bd ul {
  display: flex;
  justify-content: space-between;
}
.goods .bd li {
  width: 304px;
  height: 404px;
  background-color: #EEF9F4;
}
.goods .bd li {
  display: block;
}
.goods .bd li .pic {
  width: 304px;
  height: 304px;
}
.goods .bd li .txt {
  text-align: center;
}
.goods .bd li h4 {
  margin-top: 17px;
  margin-bottom: 8px;
  font-size: 20px;
}
.goods .bd li p {
  font-size: 18px;
  color: #AA2113;
}
.goods .bd li p span {
  font-size: 22px;
}


/* 热门品牌 */
.hot {
  margin-top: 60px;
  padding-bottom: 40px;
  overflow: hidden;
  background-color: #F5F5F5;
}
.hot .title {
  position: relative;
  margin-bottom: 40px;
}
.hot .button {
  display: flex;
  position: absolute;
  right: 0;
  top: 47px;
}
.hot .button a {
  display: block;
  width: 20px;
  height: 20px;
  background-color: #ddd;
  text-align: center;
  line-height: 20px;
  color: #fff;
}
.hot .button a:nth-child(2) {
  margin-left: 12px;
  background-color: #00BE9A;
}
.hot .bd ul {
  display: flex;
  justify-content: space-between;
}
.hot .bd li {
  width: 244px;
  height: 306px;
}

/* 最新专题 */
.topic {
  padding-top: 60px;
  margin-bottom: 40px;
}
.topic_bd ul {
  display: flex;
  justify-content: space-between;
}
.topic_bd li {
  width: 405px;
  height: 355px;
}
.topic_bd .pic {
  position: relative;
  width: 405px;
  height: 288px;
}
.topic_bd .txt {
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  height: 67px;
  line-height: 67px;
  color: #666;
  font-size: 14px;
}
.topic_bd .txt .left {
  display: flex;
}
.topic_bd .txt .left p {
  margin-right: 20px;
}
.topic_bd .txt .left .red {
  color: #AA2113;
}
.topic_bd .info {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  width: 100%;
  height: 90px;
  background-image: linear-gradient(180deg, rgba(137,137,137,0.00) 0%, rgba(0,0,0,0.90) 100%);
}
.topic_bd .info .left {
  padding-top: 20px;
  color: #fff;
}
.topic_bd .info .left h5 {
  margin-bottom: 5px;
  font-size: 20px;
}
.topic_bd .info .right {
  margin-top: 35px;
  padding: 0 7px;
  height: 25px;
  line-height: 25px;
  background-color: #fff;
  color: #AA2113;
  font-size: 15px;
}



/* 版权底部 */
.footer {
  height: 580px;
  background-color: #F5F5F5;
}
.footer .service {
  padding: 60px 0;
  height: 180px;
  border-bottom: 1px solid #E8E8E8;
}
.footer .service ul {
  display: flex;
  justify-content: space-around;
}
.footer .service li {
  display: flex;
  line-height: 58px;
}
.footer .service span {
  display: block;
  margin-right: 20px;
  width: 58px;
  height: 58px;
  background-image: url(~@/assets/images/sprite.png);
}
.footer .service li:nth-child(2) span {
  background-position: 0 -58px;
}
.footer .service li:nth-child(3) span {
  background-position: 0 -116px;
}
.footer .service li:nth-child(4) span {
  background-position: 0 -174px;
}
.footer .service p {
  font-size: 28px;
}
.footer .help {
  display: flex;
  justify-content: space-between;
  margin-top: 60px;
}
.footer .help .left {
  display: flex;
}
.footer .help .left dl {
  margin-right: 84px;
}
.footer .help .left dt {
  margin-bottom: 30px;
  font-size: 18px;
}
.footer .help .left dd {
  margin-bottom: 10px;
}
.footer .help .left dd a {
  color: #969696;
}
.footer .help .right ul {
  display: flex;
  align-items: flex-start;
}
.footer .help .right li:nth-child(1) {
  margin-right: 55px;
  text-align: center;
}
.footer .help .right div {
  margin-bottom: 10px;
  width: 120px;
  height: 120px;
  color: #969696;
}
.icon-customer-service {
  margin-left: 3px;
  color: #00BE9A;
}
.copyright {
  margin-top: 100px;
  text-align: center;
  color: #A1A1A1;
}
.copyright p {
  margin-bottom: 15px;
}
.copyright a {
  margin: 0 10px;
  color: #A1A1A1;
}

