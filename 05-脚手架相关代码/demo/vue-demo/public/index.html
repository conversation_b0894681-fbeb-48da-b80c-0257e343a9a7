<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <!-- Vue所管理的容器 将来创建结构动态渲染这个容器 -->
    <div id="app">

      <!-- 工程化开发模式中 这里不在直接编写模版语法 通过 App.vue 提供结构 -->
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
